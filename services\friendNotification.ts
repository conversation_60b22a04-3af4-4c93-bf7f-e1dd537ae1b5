// Friend notification service using Supabase
import { supabase } from '../lib/supabase';

export interface FriendNotification {
  id: string;
  type: 'request' | 'accepted' | 'message';
  userId: string;
  senderName: string;
  senderPhoto?: string;
  message?: string;
  timestamp: number;
  isRead: boolean;
}

// Get notifications for a user
export const getNotifications = async (userId: string): Promise<FriendNotification[]> => {
  return [];
};

// Mark all notifications as read
export const markAllAsRead = async (userId: string): Promise<void> => {
  console.log(`Marking all notifications as read for user ${userId}`);
};

// Send a friend request to the friend_requests table
export const sendFriendRequest = async (
  senderId: string,
  senderName: string,
  senderPhoto: string,
  receiverId: string,
  message: string
): Promise<boolean> => {
  try {
    console.log(`Sending friend request from ${senderId} to ${receiverId}: ${message}`);

    // Check if a request already exists between these users
    const { data: existingRequest, error: checkError } = await supabase
      .from('friend_requests')
      .select('id, status')
      .or(`and(sender_id.eq.${senderId},receiver_id.eq.${receiverId}),and(sender_id.eq.${receiverId},receiver_id.eq.${senderId})`)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing request:', checkError);
      return false;
    }

    if (existingRequest) {
      console.log('Friend request already exists between these users');
      return false;
    }

    // Insert new friend request
    const { error: insertError } = await supabase
      .from('friend_requests')
      .insert({
        sender_id: senderId,
        receiver_id: receiverId,
        status: 'pending',
        message: message,
        created_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Error inserting friend request:', insertError);
      return false;
    }

    console.log('Friend request sent successfully');
    return true;
  } catch (error) {
    console.error('Error in sendFriendRequest:', error);
    return false;
  }
};

// Get unread count
export const getUnreadCount = async (userId: string): Promise<number> => {
  return 0;
};

