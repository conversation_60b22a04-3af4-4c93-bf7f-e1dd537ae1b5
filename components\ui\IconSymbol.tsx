import { Ionicons } from '@expo/vector-icons';
import { Platform } from 'react-native';

interface IconSymbolProps {
  name: string;
  size: number;
  color: string;
  style?: any;
}

// Map of SF Symbol names to Ionicons names for cross-platform compatibility
const iconMap: Record<string, string> = {
  // Navigation icons
  'chevron.left': 'chevron-back',
  'chevron.right': 'chevron-forward',
  'xmark': 'close',
  
  // Tab bar icons
  'house': 'home',
  'house.fill': 'home',
  'bubble.left': 'chatbubble',
  'bubble.left.fill': 'chatbubble',
  'map': 'map',
  'map.fill': 'map',
  'person': 'person',
  'person.fill': 'person',
  'chatbubbles': 'chatbubbles',
  'calendar': 'calendar',
  
  // Common UI icons
  'plus': 'add',
  'minus': 'remove',
  'checkmark': 'checkmark',
  'arrow.clockwise': 'refresh',
  'arrow.right.square.fill': 'log-out',
  'magnifyingglass': 'search',
  'gear': 'settings',
  'bell': 'notifications',
  'bell.fill': 'notifications',
  'lock': 'lock-closed',
  'lock.fill': 'lock-closed',
  'doc.text': 'document-text',
  'doc.text.fill': 'document-text',
  'person.crop.circle.badge.plus': 'person-add',
  'person.crop.circle.badge.exclamationmark': 'alert-circle',
  'person.badge.plus': 'person-add',
  'flame.fill': 'flame',
  'info.circle.fill': 'information-circle',
  'info.circle': 'information-circle-outline',
  
  // Location and messaging
  'mappin.circle.fill': 'location',
  'location.fill': 'location',
  'location': 'location-outline',
  'message.fill': 'chatbubble-ellipses',
  'message': 'chatbubble-outline',
  'sparkles': 'sparkles',
  'envelope': 'mail-outline',
  'phone': 'call-outline',
  'ellipsis': 'ellipsis-horizontal',
  'ellipsis-horizontal': 'ellipsis-horizontal',
  
  // Profile and actions
  'heart': 'heart',
  'heart.fill': 'heart',
  'bookmark': 'bookmark-outline',
  'bookmark.fill': 'bookmark',
  'square.and.pencil': 'create',
  'photo.fill': 'image',
  'briefcase.fill': 'briefcase',
  'book.fill': 'book',
  'star.fill': 'star',
  'star': 'star-outline',
  'eye': 'eye-outline',
  'xmark.circle': 'close-circle-outline',
  'graduationcap': 'school-outline',
  'briefcase': 'briefcase-outline',
  'globe': 'globe-outline',
  'arrow.up.right': 'arrow-up-outline',
  'camera': 'camera-outline',
  
  // Grid and layout
  'circle.grid.2x2.fill': 'grid',
  'circle.grid.2x1.fill': 'grid',
  
  // Venue categories
  'cup.and.saucer.fill': 'cafe',
  'wineglass.fill': 'wine',
  'hamburger': 'fast-food',
  'leaf.fill': 'leaf',
  'paintpalette.fill': 'color-palette',
  'bag.fill': 'bag',
  'pawprint.fill': 'paw',
  'fork.knife': 'restaurant',
  'takeoutbag.and.cup.and.straw.fill': 'fast-food',
  'lock.shield.fill': 'shield-checkmark',
  'person.2.fill': 'people',
  
  // Payment app icons
  'logo-google': 'logo-google',
  'wallet-outline': 'wallet-outline',
  'phone-portrait-outline': 'phone-portrait-outline',
  'cash-outline': 'cash-outline',
  'checkmark.circle.fill': 'checkmark-circle',
  
  // Add more mappings as needed
};

// Export iconMap for use in other components
export { iconMap };

export function IconSymbol({ name, size, color, style }: IconSymbolProps) {
  // Convert SF Symbol name to Ionicons name
  const getIoniconName = (sfSymbolName: string): string => {
    // If we have a direct mapping, use it
    if (iconMap[sfSymbolName]) {
      return iconMap[sfSymbolName];
    }
    
    // For iOS, we can try to use SF Symbols directly
    if (Platform.OS === 'ios') {
      return sfSymbolName;
    }
    
    // For Android, if we don't have a mapping, use a fallback
    console.warn(`Icon mapping not found for: ${sfSymbolName}`);
    return 'help-circle-outline'; // Fallback icon
  };

  // Use Ionicons for cross-platform compatibility
  const iconName = getIoniconName(name);
  
  return (
    <Ionicons name={iconName as any} size={size} color={color} style={style} />
  );
}









