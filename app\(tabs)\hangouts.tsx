import * as Location from 'expo-location';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Linking,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import MapView, { Marker, PROVIDER_DEFAULT, UrlTile } from 'react-native-maps';
import { IconSymbol } from '../../components/ui/IconSymbol';

// Define our own MapView props interface
interface CustomMapViewProps {
  latitude: number;
  longitude: number;
  places: Place[];
}

// Free MapView component using react-native-maps with OpenStreetMap tiles
const CustomMapView = ({ latitude, longitude, places }: CustomMapViewProps) => {
  return (
    <View style={{ height: Platform.OS === 'android' ? 320 : 300, overflow: 'hidden' }}>
      <MapView
        style={{ flex: 1 }}
        provider={PROVIDER_DEFAULT}
        initialRegion={{
          latitude: latitude,
          longitude: longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
      >
        {/* OpenStreetMap tile overlay */}
        <UrlTile 
          urlTemplate="https://a.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maximumZ={19}
          flipY={false}
        />
        
        {/* User location marker */}
        <Marker
          coordinate={{ latitude, longitude }}
          title="You are here"
        >
          <View style={styles.userLocationMarker}>
            <View style={styles.userLocationMarkerCore} />
          </View>
        </Marker>
        
        {/* Place markers */}
        {places.map((place) => (
          <Marker
            key={place.id}
            coordinate={{ 
              latitude: place.latitude || latitude, 
              longitude: place.longitude || longitude 
            }}
            title={place.name}
            description={place.category}
          >
            <View style={[
              styles.placeMarker,
              Platform.OS === 'android' && { padding: 8 } // Add extra padding on Android
            ]}>
              <IconSymbol 
                name={getCategoryIcon(place.category)} 
                size={Platform.OS === 'android' ? 18 : 16} 
                color="#FFFFFF" 
              />
            </View>
          </Marker>
        ))}
      </MapView>
    </View>
  );
};

// Get color based on category for map markers
const getCategoryColor = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'restaurant':
      return '#FF5722';
    case 'café':
    case 'tea shop':
      return '#795548';
    case 'bar':
      return '#9C27B0';
    case 'fast food':
      return '#FF9800';
    case 'park':
      return '#4CAF50';
    case 'gallery':
      return '#3F51B5';
    case 'mall':
      return '#E91E63';
    case 'zoo':
      return '#8BC34A';
    default:
      return '#2196F3';
  }
};

// Get screen dimensions for responsive sizing
const { width } = Dimensions.get('window');
const CARD_WIDTH = Platform.OS === 'ios' ? width * 0.85 : width * 0.8;

// Define types for our data
interface UserLocation {
  latitude: number;
  longitude: number;
}

interface User {
  id: string;
  name: string;
  avatar: string;
}

interface Place {
  id: string;
  name: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  photos: string[];
  category: string;
  distance: string;
  isOpen: boolean;
  priceLevel: number;
  hours: string;
  usersPresent: User[];
}

// Categories for filtering
const CATEGORIES = [
  { id: 'all', name: 'All', icon: 'circle.grid.2x2.fill' },
  { id: 'restaurant', name: 'Restaurants', icon: 'fork.knife' },
  { id: 'café', name: 'Cafés', icon: 'cup.and.saucer.fill' },
  { id: 'bar', name: 'Bars', icon: 'wineglass.fill' },
  { id: 'fast food', name: 'Fast Food', icon: 'hamburger' },
  { id: 'park', name: 'Parks', icon: 'leaf.fill' },
  { id: 'mall', name: 'Shopping', icon: 'bag.fill' },
  { id: 'gallery', name: 'Arts', icon: 'paintpalette.fill' },
];

// Generate description based on category
const getDescriptionForPlace = (name: string, category: string, location?: string): string => {
  const placeName = name || `${category} in ${location || 'the area'}`;
  
  switch (category) {
    case 'Restaurant':
      return `${placeName} offers delicious meals in a welcoming atmosphere.`;
    case 'Café':
      return `${placeName} is a cozy spot for coffee and light bites.`;
    case 'Bar':
      return `${placeName} is a popular spot for drinks and socializing.`;
    case 'Park':
      return `${placeName} provides green space for recreation and relaxation.`;
    case 'Gallery':
      return `${placeName} showcases interesting exhibits and cultural artifacts.`;
    case 'Zoo':
      return `${placeName} features a collection of animals and wildlife exhibits.`;
    case 'Tea Shop':
      return `${placeName} offers a variety of teas in a relaxing environment.`;
    case 'Mall':
      return `${placeName} provides a variety of shopping options.`;
    case 'Fast Food':
      return `${placeName} offers quick service and familiar menu items.`;
    case 'Pizza':
      return `${placeName} specializes in pizza and Italian favorites.`;
    case 'Chinese':
      return `${placeName} serves authentic Chinese cuisine.`;
    case 'Asian':
      return `${placeName} offers a variety of Asian dishes.`;
    case 'Indian':
      return `${placeName} serves flavorful Indian cuisine.`;
    default:
      return `${placeName} is a local point of interest.`;
  }
};

// Format address from OSM tags
const getFormattedAddress = (tags: any, locationName: string): string => {
  if (!tags) return `Near ${locationName}`;
  
  if (tags.address) return tags.address;
  
  let address = '';
  
  // Try to build address from components
  if (tags['addr:housenumber']) address += tags['addr:housenumber'] + ' ';
  if (tags['addr:street']) address += tags['addr:street'] + ', ';
  if (tags['addr:city']) address += tags['addr:city'] + ', ';
  else if (tags['addr:suburb']) address += tags['addr:suburb'] + ', ';
  
  // If we have some address components, return them
  if (address.length > 0) return address.slice(0, -2); // Remove trailing comma and space
  
  // Fallback
  return `Near ${locationName}`;
};

// Generate a rating based on category
const generateRating = (category: string): number => {
  // Base rating between 3.5 and 4.9
  const baseRating = 3.5 + Math.random() * 1.4;
  
  // Adjust slightly based on category
  switch (category) {
    case 'Restaurant':
    case 'Café':
    case 'Tea Shop':
      return Math.min(4.9, baseRating + 0.2);
    case 'Fast Food':
      return Math.min(4.5, baseRating - 0.2);
    default:
      return baseRating;
  }
};

// Determine if a place is likely to be open based on time of day
const isPlaceOpen = (category: string): boolean => {
  const hour = new Date().getHours();
  
  switch (category) {
    case 'Restaurant':
      return hour >= 11 && hour < 22;
    case 'Café':
    case 'Tea Shop':
      return hour >= 7 && hour < 20;
    case 'Bar':
      return hour >= 16 || hour < 2;
    case 'Park':
      return hour >= 6 && hour < 22;
    case 'Gallery':
    case 'Zoo':
      return hour >= 9 && hour < 18;
    case 'Mall':
      return hour >= 10 && hour < 21;
    default:
      return hour >= 9 && hour < 20;
  }
};

// Get price level based on category
const getPriceLevelForCategory = (category: string): number => {
  switch (category) {
    case 'Restaurant':
      return Math.floor(Math.random() * 3) + 2; // 2-4
    case 'Bar':
      return Math.floor(Math.random() * 2) + 2; // 2-3
    case 'Café':
    case 'Tea Shop':
      return Math.floor(Math.random() * 2) + 1; // 1-2
    case 'Fast Food':
      return 1;
    default:
      return Math.floor(Math.random() * 3) + 1; // 1-3
  }
};

// Get hours based on category
const getHoursForCategory = (category: string): string => {
  switch (category) {
    case 'Restaurant':
      return '11:00 AM - 10:00 PM';
    case 'Café':
    case 'Tea Shop':
      return '7:00 AM - 8:00 PM';
    case 'Bar':
      return '4:00 PM - 2:00 AM';
    case 'Park':
      return '6:00 AM - 10:00 PM';
    case 'Gallery':
      return '10:00 AM - 6:00 PM';
    case 'Zoo':
      return '9:00 AM - 5:00 PM';
    case 'Mall':
      return '10:00 AM - 9:00 PM';
    default:
      return '9:00 AM - 8:00 PM';
  }
};

// Generate random users for a place
const generateRandomUsers = (category: string): User[] => {
  const users: User[] = [];
  const currentHour = new Date().getHours();
  
  // More likely to have users during peak hours
  const isPeakHour = (category === 'Restaurant' && (currentHour === 12 || currentHour === 19)) || 
                     (category === 'Bar' && currentHour >= 20 && currentHour <= 23) ||
                     (category === 'Café' && (currentHour >= 8 && currentHour <= 10 || currentHour >= 15 && currentHour <= 17)) ||
                     (category === 'Park' && currentHour >= 10 && currentHour <= 18);
  
  if (Math.random() > (isPeakHour ? 0.3 : 0.7)) {
    const numUsers = Math.floor(Math.random() * 3) + 1;
    const names = ['Alex', 'Jamie', 'Taylor', 'Jordan', 'Casey', 'Morgan', 'Riley'];
    
    for (let i = 0; i < numUsers; i++) {
      users.push({
        id: `user${Math.floor(Math.random() * 1000)}`,
        name: names[Math.floor(Math.random() * names.length)],
        avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'women' : 'men'}/${Math.floor(Math.random() * 100)}.jpg`
      });
    }
  }
  
  return users;
};

// PlaceCard component - simplified without images
const PlaceCard = ({ place, onPress, onInvite }: { place: Place, onPress: () => void, onInvite: () => void }) => {
  // Add renderPriceLevel function inside the component
  const renderPriceLevel = (level: number) => {
    let price = '';
    for (let i = 0; i < level; i++) {
      // price += '$';
    }
    return price;
  };

  // Handle opening maps
  const handleOpenMaps = () => {
    if (!place) return;
    
    // Use full address and name for more accurate location
    const query = encodeURIComponent(`${place.name}, ${place.address}`);
    
    // For iOS, use coordinates if available for more precision
    // For Android, use geo: URI scheme
    const mapsUrl = Platform.select({
      ios: place.latitude && place.longitude 
        ? `maps:?q=${place.name}&ll=${place.latitude},${place.longitude}` 
        : `maps:?q=${query}`,
      android: place.latitude && place.longitude 
        ? `geo:${place.latitude},${place.longitude}?q=${encodeURIComponent(place.name)}` 
        : `geo:0,0?q=${query}`,
    });
    
    if (mapsUrl) {
      Linking.openURL(mapsUrl).catch((err: Error) => {
        console.error('Error opening maps:', err);
        Alert.alert(
          'Navigation Error',
          'Unable to open maps. Please make sure you have a maps app installed.',
          [{ text: 'OK' }]
        );
      });
    }
  };

  return (
    <TouchableOpacity style={styles.placeCard} onPress={onPress}>
      <View style={styles.categoryBadge}>
        <Text style={styles.categoryBadgeText}>{place.category}</Text>
      </View>
      
      <View style={styles.placeInfo}>
        <View style={styles.placeHeader}>
          <Text style={styles.placeName} numberOfLines={1}>{place.name}</Text>
          <Text style={styles.placePrice}>{renderPriceLevel(place.priceLevel)}</Text>
        </View>
        
        <View style={styles.placeDetails}>
          <View style={styles.placeRating}>
            <IconSymbol name="star.fill" size={14} color="#FFD700" />
            <Text style={styles.ratingText}>
              {place.rating !== undefined ? place.rating.toFixed(1) : "N/A"}
            </Text>
          </View>
          
          <Text style={styles.placeCategory}>{place.category}</Text>
          <Text style={styles.placeDistance}>{place.distance}</Text>
        </View>
        
        <Text style={styles.placeAddress} numberOfLines={1}>
          {place.address}
        </Text>
        
        {/* Action buttons */}
        <View style={styles.placeActions}>
          <TouchableOpacity 
            style={styles.inviteButton}
            onPress={onInvite}
          >
            <IconSymbol name="person.2.fill" size={16} color="#FFFFFF" />
            <Text style={styles.inviteButtonText}>Invite Friends</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.directionsButton}
            onPress={handleOpenMaps}
          >
            <IconSymbol name="location.fill" size={16} color="#FFFFFF" />
            <Text style={styles.directionsButtonText}>Directions</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function HangoutsScreen() {
  const [places, setPlaces] = useState<Place[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<Place[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [currentRadius, setCurrentRadius] = useState(2000); // Start with 2km
  const [fetchingMorePlaces, setFetchingMorePlaces] = useState(false);
  const [allRadiusesSearched, setAllRadiusesSearched] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [showMap, setShowMap] = useState(false);
  
  // Reference to FlatList for scroll handling
  const flatListRef = useRef<FlatList>(null);
  
  // Define radius steps for progressive loading
  const radiusSteps = [2000, 4000, 6000, 10000, 15000, 20000, 30000]; // 2km to 30km
  
  // Effect hooks
  useEffect(() => {
    getUserLocationAndPlaces();
  }, []);
  
  // Filter places when category changes
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredPlaces(places);
    } else {
      const filtered = places.filter(place => 
        place.category.toLowerCase() === selectedCategory.toLowerCase()
      );
      setFilteredPlaces(filtered);
    }
  }, [selectedCategory, places]);
  
  // Function to get user location and fetch nearby places
  const getUserLocationAndPlaces = async () => {
    setLoading(true);
    setErrorMsg(null);
    setCurrentRadius(radiusSteps[0]); // Reset to first radius
    setAllRadiusesSearched(false);
    
    try {
      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        setLoading(false);
        return;
      }
      
      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced
      });
      
      setUserLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });
      
      // Fetch nearby places starting with smallest radius
      await fetchPlacesWithRadius(location.coords.latitude, location.coords.longitude, radiusSteps[0]);
      
    } catch (error) {
      console.error('Error getting location:', error);
      setErrorMsg('Failed to get location. Please check your settings.');
      setLoading(false);
    }
  };
  
  // Function to fetch places with a specific radius
  const fetchPlacesWithRadius = async (latitude: number, longitude: number, radius: number) => {
    try {
      if (radius === radiusSteps[0]) {
        setLoading(true);
      } else {
        setFetchingMorePlaces(true);
      }
      
      console.log(`Fetching places within ${radius/1000}km...`);
      
      // Build query for the specific radius
      const query = `
        [out:json];
        (
          // Food and drinks
          node["amenity"~"restaurant|cafe|bar|pub|fast_food"](around:${radius},${latitude},${longitude});
          
          // Popular chains and specific types
          node["name"~"McDonald|KFC|Domino|Pizza|Burger King|Subway|Starbucks|Chinese|Wok|Momos|Wow"](around:${radius},${latitude},${longitude});
          
          // Tea shops
          node["shop"="tea"](around:${radius},${latitude},${longitude});
          node["name"~".*[tT][eE][aA].*"](around:${radius},${latitude},${longitude});
          
          // Entertainment and leisure
          node["leisure"~"park|garden"](around:${radius},${latitude},${longitude});
          node["tourism"~"museum|gallery"](around:${radius},${latitude},${longitude});
          
          // Zoo
          node["tourism"="zoo"](around:${radius},${latitude},${longitude});
          node["name"~"zoo|Zoo|ZOO"](around:${radius},${latitude},${longitude});
          
          // Shopping
          node["shop"="mall"](around:${radius},${latitude},${longitude});
        );
        out body;
      `;
      
      const url = `https://overpass-api.de/api/interpreter?data=${encodeURIComponent(query)}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      // Get location name for better descriptions
      const locationName = await getLocationNameFromCoordinates(latitude, longitude);
      
      // Process the results
      let newPlaces: Place[] = [];
      
      if (data.elements && data.elements.length > 0) {
        newPlaces = processApiData(data, locationName, latitude, longitude);
        
        // Filter out duplicates
        const existingIds = new Set(places.map(p => p.id));
        newPlaces = newPlaces.filter(p => !existingIds.has(p.id));
        
        // Add distance filter - only include places within the current radius
        newPlaces = newPlaces.filter(place => {
          const distance = parseFloat(place.distance.split(' ')[0]);
          const unit = place.distance.includes('km') ? 1000 : 1; // Convert to meters
          return distance * unit <= radius;
        });
        
        // Update places state
        const updatedPlaces = [...places, ...newPlaces];
        
        // Sort by distance
        updatedPlaces.sort((a, b) => {
          const distA = parseFloat(a.distance.split(' ')[0]);
          const distB = parseFloat(b.distance.split(' ')[0]);
          const unitA = a.distance.includes('km') ? 1000 : 1;
          const unitB = b.distance.includes('km') ? 1000 : 1;
          return (distA * unitA) - (distB * unitB);
        });
        
        setPlaces(updatedPlaces);
        
        // Update filtered places based on selected category
        if (selectedCategory === 'all') {
          setFilteredPlaces(updatedPlaces);
        } else {
          setFilteredPlaces(updatedPlaces.filter(place => 
            place.category.toLowerCase() === selectedCategory.toLowerCase()
          ));
        }
        
        // Set selected place if none is selected yet
        if (!selectedPlace && updatedPlaces.length > 0) {
          setSelectedPlace(updatedPlaces[0]);
        }
      }
      
      console.log(`Found ${newPlaces.length} new places within ${radius/1000}km`);
      
      // Check if this is the last radius
      const currentRadiusIndex = radiusSteps.indexOf(radius);
      if (currentRadiusIndex === radiusSteps.length - 1) {
        setAllRadiusesSearched(true);
      } else {
        // Set the next radius for future loading
        setCurrentRadius(radiusSteps[currentRadiusIndex + 1]);
      }
      
      return newPlaces.length;
      
    } catch (error) {
      console.error(`Error fetching places within ${radius/1000}km:`, error);
      return 0;
    } finally {
      setLoading(false);
      setFetchingMorePlaces(false);
      setRefreshing(false);
    }
  };
  
  // Process data from API response - simplified without photos
  const processApiData = (data: any, locationName: string, latitude: number, longitude: number): Place[] => {
    const places: Place[] = [];
    
    if (data.elements) {
      data.elements.forEach((element: any) => {
        // Skip elements without name or proper tags
        if (!element.tags || !element.tags.name) return;
        
        // Extract basic info
        const name = element.tags.name;
        const category = getCategoryFromTags(element.tags);
        
        // Skip if no valid category
        if (!category) return;
        
        // Calculate distance in kilometers
        const distance = calculateDistance(latitude, longitude, element.lat, element.lon);
        const distanceStr = distance < 1 ? 
          `${(distance * 1000).toFixed(0)} m` : 
          `${distance.toFixed(1)} km`;
        
        // Generate random rating between 3.5 and 5.0
        const rating = generateRating(category);
        
        // Generate random price level (1-4)
        const priceLevel = Math.floor(Math.random() * 4) + 1;
        
        // Format address
        const address = getFormattedAddress(element.tags, locationName);
        
        // Generate description
        const description = getDescriptionForPlace(name, category, locationName);
        
        // Generate random opening hours
        const isOpen = Math.random() > 0.3; // 70% chance of being open
        const hours = isOpen ? 
          `${Math.floor(Math.random() * 3) + 8}:00 AM - ${Math.floor(Math.random() * 4) + 8}:00 PM` : 
          'Closed';
        
        // Create place object - no photos needed
        places.push({
          id: element.id.toString(),
          name,
          description,
          address,
          latitude: element.lat,
          longitude: element.lon,
          rating: rating || 4.0, // Provide default rating if undefined
          photos: [], // Empty array since we're not using photos
          category,
          distance: distanceStr,
          isOpen,
          priceLevel,
          hours,
          usersPresent: []
        });
      });
    }
    
    return places;
  };
  
  // Handle end reached (user scrolled to bottom)
  const handleEndReached = () => {
    if (!loading && !fetchingMorePlaces && !allRadiusesSearched && userLocation) {
      console.log('User scrolled to bottom, loading more places...');
      fetchPlacesWithRadius(userLocation.latitude, userLocation.longitude, currentRadius);
    }
  };
  
  // Function to handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await getUserLocationAndPlaces();
  };
  
  // Function to handle venue details
  const handleViewVenueDetails = (place: Place) => {
    // Navigate to venue details screen with all place data
    router.push({
      pathname: '/venue/[id]',
      params: { 
        id: place.id,
        name: place.name,
        category: place.category,
        rating: place.rating?.toString() || "N/A",
        address: place.address,
        description: place.description,
        priceLevel: place.priceLevel?.toString(),
        latitude: place.latitude?.toString(),
        longitude: place.longitude?.toString(),
        // Convert photos array to a string that can be passed as a param
        photos: place.photos ? JSON.stringify(place.photos) : "[]",
        hours: place.hours,
        isOpen: place.isOpen ? "true" : "false",
        distance: place.distance
      }
    });
  };
  
  // Function to handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    // Scroll back to top when changing categories
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({ offset: 0, animated: true });
    }
  };
  
  // Function to handle inviting friends to a place
  const handleInvite = (place: Place) => {
    // Show modal or navigate to invite screen with place details
    router.push({
      pathname: '/invite-friends',
      params: {
        placeId: place.id,
        placeName: place.name,
        placeCategory: place.category,
        placeAddress: place.address,
        placePhoto: place.photos && place.photos.length > 0 ? place.photos[0] : ""
      }
    });
  };
  
  // Function to render price level
  const renderPriceLevel = (level: number) => {
    let price = '';
    for (let i = 0; i < level; i++) {
      // price += '$';
    }
    return price;
  };
  
  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF4B00" />
        <Text style={styles.loadingText}>Finding places near you...</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hangout Spots</Text>
        <TouchableOpacity 
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <IconSymbol name="arrow.clockwise" size={20} color="#333" />
        </TouchableOpacity>
      </View>
      
      {/* Categories */}
      <View style={styles.filterContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
          contentContainerStyle={styles.categoriesContent}
        >
          {CATEGORIES.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect(category.id)}
              activeOpacity={0.7}
            >
              <IconSymbol 
                name={category.icon} 
                size={20} 
                color={selectedCategory === category.id ? "#FFFFFF" : "#333333"} 
              />
              <Text 
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Loading state */}
      {loading && places.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF4B00" />
          <Text style={styles.loadingText}>Finding places near you...</Text>
        </View>
      ) : errorMsg ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMsg}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={getUserLocationAndPlaces}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        // Place Cards
        <FlatList
          ref={flatListRef}
          data={filteredPlaces}
          contentContainerStyle={styles.listContent}
          renderItem={({ item }) => (
            <PlaceCard 
              place={item} 
              onPress={() => handleViewVenueDetails(item)}
              onInvite={() => handleInvite(item)}
            />
          )}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#FF4B00']}
              tintColor="#FF4B00"
            />
          }
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            fetchingMorePlaces ? (
              <View style={styles.footerLoader}>
                <ActivityIndicator size="small" color="#FF4B00" />
                <Text style={styles.footerLoaderText}>Loading more places...</Text>
              </View>
            ) : !allRadiusesSearched && filteredPlaces.length > 0 ? (
              <TouchableOpacity 
                style={styles.loadMoreButton}
                onPress={() => {
                  if (userLocation) {
                    fetchPlacesWithRadius(userLocation.latitude, userLocation.longitude, currentRadius);
                  }
                }}
              >
                <Text style={styles.loadMoreButtonText}>Load More Places</Text>
              </TouchableOpacity>
            ) : null
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                No places found for this category.
              </Text>
              <TouchableOpacity 
                style={styles.changeFilterButton}
                onPress={() => setSelectedCategory('all')}
              >
                <Text style={styles.changeFilterButtonText}>Show All Categories</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    marginBottom: 8,
  },
  categoriesContainer: {
    maxHeight: 60,
  },
  categoriesContent: {
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 25,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  categoryButtonActive: {
    backgroundColor: '#FF4B00',
    borderColor: '#FF4B00',
  },
  categoryText: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 8,
    color: '#333333',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  errorContainer: {
    backgroundColor: '#FFF3F3',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FFD7D7',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#D32F2F',
    flex: 1,
  },
  retryButton: {
    backgroundColor: '#FF4B00',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginLeft: 10,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  listContent: {
    padding: 15,
  },
  placeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 8, // Reduced from 16 to 8
    marginBottom: Platform.OS === 'android' ? 16 : 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: Platform.OS === 'android' ? 4 : 0,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    overflow: 'hidden'
  },
  placeInfo: {
      padding: 10,
    },
  placeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  placeName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  placePrice: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  placeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  placeRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  ratingText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  placeCategory: {
    fontSize: 14,
    color: '#666',
    marginRight: 12,
  },
  placeDistance: {
    fontSize: 14,
    color: '#666',
  },
  placeAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  categoryBadge: {
      position: 'absolute',
      top: 0,
      right: 10,
      backgroundColor: '#FF4B00',
      borderRadius: 10,
      paddingHorizontal: 8,
      paddingVertical: 4,
      zIndex: 1,
    },
  categoryBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  placeActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  inviteButton: {
    backgroundColor: '#FF4B00',
    borderRadius: 8,
    paddingVertical: Platform.OS === 'android' ? 10 : 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginRight: 8,
  },
  inviteButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  directionsButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: Platform.OS === 'android' ? 10 : 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginLeft: 8,
  },
  directionsButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  usersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  usersTitle: {
    fontSize: 14,
    color: '#333',
    marginRight: 10,
  },
  avatarRow: {
    flexDirection: 'row',
  },
  userAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  moreAvatars: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FF4B00',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -10,
  },
  moreAvatarsText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  footerLoader: {
    padding: 20,
    alignItems: 'center',
  },
  footerLoaderText: {
    marginTop: 8,
    color: '#666',
  },
  loadMoreButton: {
    backgroundColor: '#FF4B00',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    alignSelf: 'center',
    marginVertical: 15,
  },
  loadMoreButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  changeFilterButton: {
    backgroundColor: '#FF4B00',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  changeFilterButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  noImageContainer: {
    height: 150,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  noImageText: {
    color: '#999999',
    marginTop: 8,
    fontSize: 14,
  },
  userLocationMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userLocationMarkerCore: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  placeMarker: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FF4B00',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  placeMarkerText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  viewToggleContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 20,
    padding: 4,
  },
  viewToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    flex: 1,
  },
  viewToggleButtonActive: {
    backgroundColor: '#FF4B00',
  },
  viewToggleText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666666',
  },
  viewToggleTextActive: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  mapContainer: {
    flex: 1,
    height: Dimensions.get('window').height * 0.7,
    marginHorizontal: 10,
    marginBottom: 10,
    borderRadius: 12,
    overflow: 'hidden',
  },
});

// Function to get location name from coordinates using OpenStreetMap Nominatim
const getLocationNameFromCoordinates = async (latitude: number, longitude: number): Promise<string> => {
  try {
    // Add a user-agent header to comply with Nominatim usage policy
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`,
      {
        headers: {
          'User-Agent': 'HeatwavesApp/1.0',
          'Accept': 'application/json'
        }
      }
    );
    
    // Check if response is OK
    if (!response.ok) {
      console.log(`Nominatim API error: ${response.status} ${response.statusText}`);
      return 'your area';
    }
    
    // Get response text first to check if it's valid JSON
    const text = await response.text();
    
    // Try to parse as JSON
    let data;
    try {
      data = JSON.parse(text);
    } catch (parseError) {
      console.log('Failed to parse Nominatim response:', text.substring(0, 100) + '...');
      return 'your area';
    }
    
    // Extract city or area name
    const locationName = data.address?.city || 
                         data.address?.town || 
                         data.address?.village || 
                         data.address?.suburb ||
                         'your area';
    
    return locationName;
  } catch (error) {
    console.error('Error getting location name:', error);
    return 'your area';
  }
};

// Function to get photo URL based on category
const getCategoryPhoto = (category: string): string => {
  // Generate appropriate photo based on category
  let photoUrl = '';
  switch (category) {
    case 'Café':
    case 'Tea Shop':
      photoUrl = 'https://images.unsplash.com/photo-1554118811-1e0d58224f24';
      break;
    case 'Restaurant':
      photoUrl = 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5';
      break;
    case 'Fast Food':
      photoUrl = 'https://images.unsplash.com/photo-1552566626-52f8b828add9';
      break;
    case 'Bar':
      photoUrl = 'https://images.unsplash.com/photo-1470337458703-46ad1756a187';
      break;
    case 'Park':
      photoUrl = 'https://images.unsplash.com/photo-1519331379826-f10be5486c6f';
      break;
    case 'Gallery':
      photoUrl = 'https://images.unsplash.com/photo-1577495508048-b635879837f1';
      break;
    case 'Mall':
      photoUrl = 'https://images.unsplash.com/photo-1441986300917-64674bd600d8';
      break;
    case 'Zoo':
      photoUrl = 'https://images.unsplash.com/photo-1503919545889-aef636e10ad4';
      break;
    case 'Chinese':
      photoUrl = 'https://images.unsplash.com/photo-1563245372-f21724e3856d';
      break;
    case 'Pizza':
      photoUrl = 'https://images.unsplash.com/photo-1565299624946-b28f40a0ae38';
      break;
    case 'Indian':
      photoUrl = 'https://images.unsplash.com/photo-1585937421612-70a008356c36';
      break;
    default:
      photoUrl = 'https://images.unsplash.com/photo-1519331379826-f10be5486c6f';
  }
  
  // Add random parameters to avoid caching
  photoUrl += `?random=${Math.floor(Math.random() * 1000)}`;
  return photoUrl;
};

// Helper function to calculate distance between coordinates
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2); 
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
  const distance = R * c; // Distance in km
  return distance;
};

const deg2rad = (deg: number): number => {
  return deg * (Math.PI/180);
};

// Function to determine category from OSM tags
const getCategoryFromTags = (tags: any): string => {
  // Check for amenity tag first
  if (tags.amenity) {
    switch (tags.amenity.toLowerCase()) {
      case 'restaurant':
        return 'Restaurant';
      case 'cafe':
        return 'Café';
      case 'bar':
      case 'pub':
        return 'Bar';
      case 'fast_food':
        return 'Fast Food';
    }
  }
  
  // Check for shop tag
  if (tags.shop) {
    switch (tags.shop.toLowerCase()) {
      case 'tea':
        return 'Tea Shop';
      case 'mall':
      case 'supermarket':
        return 'Mall';
    }
  }
  
  // Check for leisure tag
  if (tags.leisure) {
    switch (tags.leisure.toLowerCase()) {
      case 'park':
      case 'garden':
        return 'Park';
    }
  }
  
  // Check for tourism tag
  if (tags.tourism) {
    switch (tags.tourism.toLowerCase()) {
      case 'museum':
      case 'gallery':
        return 'Gallery';
      case 'zoo':
        return 'Zoo';
    }
  }
  
  // Check name for specific keywords
  if (tags.name) {
    const name = tags.name.toLowerCase();
    if (name.includes('pizza') || name.includes('domino')) {
      return 'Pizza';
    }
    if (name.includes('burger') || name.includes('mcdonald') || name.includes('kfc')) {
      return 'Fast Food';
    }
    if (name.includes('starbucks')) {
      return 'Café';
    }
    if (name.includes('tea')) {
      return 'Tea Shop';
    }
    if (name.includes('chinese') || name.includes('wok')) {
      return 'Chinese';
    }
    if (name.includes('indian') || name.includes('momos')) {
      return 'Indian';
    }
    if (name.includes('zoo')) {
      return 'Zoo';
    }
  }
  
  // Check cuisine tag
  if (tags.cuisine) {
    const cuisine = tags.cuisine.toLowerCase();
    if (cuisine.includes('pizza')) {
      return 'Pizza';
    }
    if (cuisine.includes('chinese')) {
      return 'Chinese';
    }
    if (cuisine.includes('indian')) {
      return 'Indian';
    }
    if (cuisine.includes('asian')) {
      return 'Asian';
    }
  }
  
  // Default to a generic category based on available tags
  if (tags.amenity === 'restaurant') {
    return 'Restaurant';
  }
  
  // If we can't determine a specific category, return a default
  return 'Restaurant';
};

// Add this function to get icon based on category
const getCategoryIcon = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'restaurant':
      return 'fork.knife';
    case 'café':
    case 'tea shop':
      return 'cup.and.saucer.fill';
    case 'bar':
      return 'wineglass.fill';
    case 'fast food':
      return 'hamburger';
    case 'park':
      return 'leaf.fill';
    case 'gallery':
      return 'paintpalette.fill';
    case 'mall':
      return 'bag.fill';
    case 'zoo':
      return 'pawprint.fill';
    case 'chinese':
      return 'takeoutbag.and.cup.and.straw.fill';
    case 'pizza':
      return 'circle.grid.2x1.fill';
    case 'indian':
      return 'flame.fill';
    default:
      return 'mappin.circle.fill';
  }
};


