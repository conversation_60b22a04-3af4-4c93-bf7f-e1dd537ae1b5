import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    Dimensions,
    Image,
    PanResponder,
    Platform,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';

// Helper function to get full image URL from Supabase storage
const getImageUrl = (imagePath: string, bucket: 'images' | 'avatars' = 'images') => {
  if (!imagePath) return null;

  // If it's already a full URL (http/https), return as is
  if (imagePath.startsWith('http')) return imagePath;

  // If it's a local file path, return as is (for local images)
  if (imagePath.startsWith('file://')) return imagePath;

  // Construct Supabase storage URL for relative paths
  const supabaseUrl = 'https://upijhrlbwqliwyxqamfx.supabase.co';
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${imagePath}`;
};

// First, install the expo-ads-admob package
// Run: npx expo install expo-ads-admob

// Define the LocationType interface
interface LocationType {
  latitude: number;
  longitude: number;
}

// Fix the Animated.View type issues by importing the correct types
import { View as RNView } from 'react-native';
type AnimatedViewProps = Animated.AnimatedProps<RNView['props']>;

import MessageInputModal from '../../components/MessageInputModal';
import { Button } from '../../components/ui/Button';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { ThemedText } from '../../components/ui/ThemedText';
import { Colors } from '../../constants/Colors';

// Import the real AuthContext
import { useAuth } from '../../context/AuthContext';
import { sendFriendRequest } from '../../services/friendNotification';

// Get screen dimensions
const { width, height } = Dimensions.get('window');

// Define responsive heights based on percentages
const HEADER_HEIGHT_PERCENT = 0.10; // 10% for header
const CARD_SECTION_HEIGHT_PERCENT = Platform.OS === 'android' ? 0.75 : 0.70; // Adjusted for Android to prevent overlap
const NAV_HEIGHT_PERCENT = Platform.OS === 'android' ? 0.15 : 0.10; // Adjusted for Android to ensure tab bar visibility
const GAP_PERCENT = 0.025; // 2.5% for gaps

// Calculate actual heights
const HEADER_HEIGHT = height * HEADER_HEIGHT_PERCENT;
const CARD_SECTION_HEIGHT = height * CARD_SECTION_HEIGHT_PERCENT;
const NAV_HEIGHT = height * NAV_HEIGHT_PERCENT;
const GAP_HEIGHT = height * GAP_PERCENT;

// Adjust card height to be responsive to different screen sizes
const CARD_HEIGHT = CARD_SECTION_HEIGHT; // Card height is the card section height

// Increase the gap to 10px on each side
const SIDE_GAP = Platform.OS === 'android' ? 5 : 10; // Further reduced side gap for wider cards on Android
// Adjust card width to account for the gaps
const CARD_WIDTH = width - (SIDE_GAP * 2);



// Import Supabase client
import { supabase } from '../../lib/supabase';

// Define the Profile interface to match your Supabase table
interface Profile {
  id: string;
  name: string;
  age?: number;
  bio?: string;
  photoURL?: string;
  images?: string[]; // Add images array
  distance?: number; // Add distance
  interests?: string[]; // Add interests
  location?: string; // Add location
  hobbies?: string[]; // Add hobbies
}

// Mock profiles for testing
const mockProfiles = [
  {
    name: 'Emma',
    age: 28,
    bio: 'Adventure seeker and coffee enthusiast',
    interests: ['Hiking', 'Photography', 'Travel']
  },
  {
    name: 'James',
    age: 32,
    bio: 'Foodie and fitness lover',
    interests: ['Cooking', 'Running', 'Reading']
  },
  {
    name: 'Sophia',
    age: 26,
    bio: 'Art director with a passion for design',
    interests: ['Painting', 'Museums', 'Fashion']
  },
  {
    name: 'Michael',
    age: 30,
    bio: 'Tech entrepreneur and dog lover',
    interests: ['Startups', 'Dogs', 'Hiking']
  },
  {
    name: 'Olivia',
    age: 27,
    bio: 'Yoga instructor and plant mom',
    interests: ['Yoga', 'Plants', 'Meditation']
  }
];

// Define proper props interface for the DiscoveryCard component
interface DiscoveryCardProps {
  item: Profile;
  onSendFriendRequest: (userId: string) => void;
  onSaveProfile: (userId: string) => void;
  onViewDetails: (profile: Profile) => void;
  isFriendRequested: boolean;
  isSaved: boolean;
  colorScheme: string | null;
}

// Update getProfiles function to fetch from Supabase
const getProfiles = async (
  userId: string,
  gender?: string,
  preference?: string,
  limit?: number,
  lastVisible?: any
): Promise<{
  profiles: Profile[];
  lastVisible: any;
}> => {
  try {
    // Start building the query
    let query = supabase
      .from('profiles')
      .select('id, display_name, avatar_url, photos, bio, gender, preference, location, hobbies')
      .neq('id', userId) // Don't show the user's own profile
      .order('created_at', { ascending: false });
      


    // Add filters if provided
    if (gender) {
      query = query.eq('gender', gender);
    }

    if (preference) {
      query = query.eq('preference', preference);
    }

    // Add pagination
    if (limit) {
      query = query.limit(limit);
    }

    if (lastVisible) {
      query = query.gt('id', lastVisible); // Assuming 'id' is a good cursor for pagination
    }

    const { data, error } = await query;

    if (error && error.code === '42703' && error.message.includes('column "age" does not exist')) {
      console.warn('Age column not found, retrying without age column');
      let retryQuery = supabase
        .from('profiles')
        .select('id, display_name, avatar_url, photos, bio, gender, preference, location, hobbies')
        .neq('id', userId) // Don't show the user's own profile
        .order('created_at', { ascending: false });

      if (gender) {
        retryQuery = retryQuery.eq('gender', gender);
      }

      if (preference) {
        retryQuery = retryQuery.eq('preference', preference);
      }

      if (limit) {
        retryQuery = retryQuery.limit(limit);
      }

      if (lastVisible) {
        retryQuery = retryQuery.gt('id', lastVisible);
      }

      const { data: retryData, error: retryError } = await retryQuery;

      if (retryError) {
        console.error('Error fetching profiles after retry:', retryError);
        console.log('getProfiles (retry) returning:', { profiles: [], lastVisible: null });
        return { profiles: [], lastVisible: null };
      }

      // Fetch discover card images for all profiles in retry
      const profilesData: Profile[] = await Promise.all(retryData.map(async (profile: any) => {
        // Fetch discover card images for this profile
        const { data: imagesData, error: imagesError } = await supabase
          .from('discover_card_images')
          .select('image_url')
          .eq('user_id', profile.id)
          .order('created_at', { ascending: false });

        if (imagesError) {
          console.error(`Error fetching discover images for ${profile.display_name}:`, imagesError);
        }

        // Process images from discover_card_images table
        const discoverImages = imagesData?.map(img => getImageUrl(img.image_url, 'images')).filter(Boolean) || [];
        const avatarUrl = profile.avatar_url ? getImageUrl(profile.avatar_url, 'avatars') : null;

        const images = discoverImages.length > 0
          ? discoverImages
          : (avatarUrl ? [avatarUrl] : []);

        return {
          id: profile.id,
          name: profile.display_name,
          bio: profile.bio,
          photoURL: avatarUrl,
          images: images,
          gender: profile.gender,
          preference: profile.preference,
          location: profile.location,
          hobbies: profile.hobbies,
          age: profile.birthday_date ? new Date().getFullYear() - new Date(profile.birthday_date).getFullYear() : undefined,
        };
      }));

      console.log('getProfiles (retry) returning:', { profiles: profilesData || [], lastVisible: retryData && retryData.length > 0 ? retryData[retryData.length - 1].id : null });
      return {
        profiles: profilesData || [],
        lastVisible: retryData && retryData.length > 0 ? retryData[retryData.length - 1].id : null
      };
    }

    if (error) {
      console.error('Error fetching profiles:', error);
      console.log('getProfiles returning:', { profiles: [], lastVisible: null });
      return { profiles: [], lastVisible: null };
    }

    // Fetch discover card images for all profiles
    const profilesData: Profile[] = await Promise.all(data.map(async (profile: any) => {
      // Fetch discover card images for this profile
      const { data: imagesData, error: imagesError } = await supabase
        .from('discover_card_images')
        .select('image_url')
        .eq('user_id', profile.id)
        .order('created_at', { ascending: false });

      if (imagesError) {
        console.error(`Error fetching discover images for ${profile.display_name}:`, imagesError);
      }

      // Process images from discover_card_images table
      const discoverImages = imagesData?.map(img => getImageUrl(img.image_url, 'images')).filter(Boolean) || [];
      const avatarUrl = profile.avatar_url ? getImageUrl(profile.avatar_url, 'avatars') : null;

      const images = discoverImages.length > 0
        ? discoverImages
        : (avatarUrl ? [avatarUrl] : []);

      // Debug logging
      console.log(`Profile ${profile.display_name}:`, {
        discoverImages: imagesData?.map(img => img.image_url),
        avatar_url: profile.avatar_url,
        processedDiscoverImages: discoverImages,
        avatarUrl,
        finalImages: images
      });

      return {
        id: profile.id,
        name: profile.display_name,
        bio: profile.bio,
        photoURL: avatarUrl,
        images: images,
        gender: profile.gender,
        preference: profile.preference,
        location: profile.location,
        hobbies: profile.hobbies,
        age: profile.age, // Assign the age directly if it exists in the profile object
      };
    }));

    console.log('getProfiles returning:', { profiles: profilesData || [], lastVisible: data && data.length > 0 ? data[data.length - 1].id : null });
    return {
      profiles: profilesData || [],
      lastVisible: data && data.length > 0 ? data[data.length - 1].id : null
    };
  } catch (error) {
    console.error('Error in getProfiles:', error);
    console.log('getProfiles (catch) returning:', { profiles: [], lastVisible: null });
    return { profiles: [], lastVisible: null };
  }
};

// Define the component first
const DiscoveryCardComponent = ({ 
  item,
  onSendFriendRequest,
  onSaveProfile,
  onViewDetails,
  isFriendRequested,
  isSaved,
  colorScheme
}: DiscoveryCardProps) => {
  console.log('Rendering DiscoveryCardComponent for item:', item.id, item.name);
  console.log('Item images:', item.images);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const isIOS = Platform.OS === 'ios';
  
  const handleImageScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = width - (SIDE_GAP * 2);
    const newIndex = Math.round(contentOffsetX / cardWidth);
    if (newIndex !== currentImageIndex) {
      setCurrentImageIndex(newIndex);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        { backgroundColor: Colors.dark.card } // Set to black for both dark and light
      ]}
      onPress={() => onViewDetails(item)} // Make the entire card clickable
      activeOpacity={0.8}
    >
      {/* Upper section - Images with fully rounded corners */} 
      <View style={styles.cardUpperSection}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleImageScroll}
          scrollEventThrottle={16}
          style={styles.imageContainer}
        >
          {item.images && item.images.length > 0 ? (
            item.images.map((image, index) => (
              <Image
                key={index}
                source={{ uri: image }}
                style={styles.image}
                resizeMode="cover"
                onError={(error) => {
                  console.log('Image load error for:', image, error.nativeEvent.error);
                }}
                onLoad={() => {
                  console.log('Image loaded successfully:', image);
                }}
              />
            ))
          ) : (
            <View style={[styles.image, styles.noImagePlaceholder]}>
              <IconSymbol name="person" size={80} color="#666666" />
              <Text style={styles.noImageText}>No Photo</Text>
            </View>
          )}
        </ScrollView>
        
        {/* Image indicators */}
        <View style={styles.imageIndicators}>
          {item.images?.map((_, index) => (
            <View
              key={index}
              style={[
                styles.imageIndicator,
                {
                  width: index === currentImageIndex ? 24 : 8,
                  opacity: index === currentImageIndex ? 1 : 0.6
                }
              ]}
            />
          ))}
        </View>
      </View>
      
      {/* Lower section - Details with theme-appropriate background */}
      <View style={styles.cardLowerSection}>
        <LinearGradient
          colors={[Colors.dark.card, Colors.dark.background]}
          style={styles.lowerSectionGradient}
        >
          <View style={styles.cardContentContainer}>
            <View style={styles.cardMainContent}>
              <View style={styles.nameAgeContainer}>
                <Text style={[
                  styles.nameText,
                   { color: Colors.primary } // Use reddish orange color for name
                ]}>
                  {item.name}
                </Text>
              </View>
            </View>
            
            {/* Action buttons - now on the right side for both platforms */}
            <View style={styles.sideButtonsContainer}>
              <TouchableOpacity 
                style={styles.sideActionButton}
                onPress={() => onSaveProfile(item.id)}
              >
                <IconSymbol 
                  name={isSaved ? "bookmark.fill" : "bookmark"} 
                  size={22} 
                  color={isSaved ? Colors.secondary : Colors.primary} 
                />
             </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.sideActionButton}
                onPress={() => onSendFriendRequest(item.id)}
              >
                <IconSymbol 
                  name={isFriendRequested ? "checkmark" : "plus"} 
                  size={22} 
                  color={Colors.primary} 
                />
             </TouchableOpacity>
              

            </View>
          </View>
        </LinearGradient>
      </View>
    </TouchableOpacity>
  );
};

// Then memoize it using React's memo
const DiscoveryCard = React.memo(DiscoveryCardComponent);

export default function DiscoveryScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();
  const [discoveries, setDiscoveries] = useState<Profile[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
console.log('DiscoveryScreen: currentIndex', currentIndex, 'discoveries.length', discoveries.length);
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sentRequests, setSentRequests] = useState<string[]>([]);
  const [savedProfiles, setSavedProfiles] = useState<string[]>([]);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>('');
  const [selectedUserName, setSelectedUserName] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);
  const [userPhotos, setUserPhotos] = useState<string[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [friendRequestCount, setFriendRequestCount] = useState(0);
  const [userLocation, setUserLocation] = useState<LocationType | null>(null);
  const userName = user?.displayName || 'User';
  
  // Create a new Animated.ValueXY for position tracking
  const position = useRef(new Animated.ValueXY()).current;
  
  // Function to get theme color
  const getThemeColor = (colorName: string): string => {
    const colors: Record<string, string> = {
      background: Colors.dark.background,
      card: Colors.dark.card,
      text: Colors.dark.text,
      tint: '#FF4B00'
    };
    return colors[colorName] || colors.text;
  };

  // Function to fetch friend request count
  const fetchFriendRequestCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      const { count, error } = await supabase
        .from('friend_requests')
        .select('*', { count: 'exact', head: true })
        .eq('receiver_id', user.id)
        .eq('status', 'pending');

      if (error) {
        console.error('Error fetching friend request count:', error);
        return;
      }

      setFriendRequestCount(count || 0);
    } catch (error) {
      console.error('Error in fetchFriendRequestCount:', error);
    }
  }, [user?.id]);

    const loadMoreProfiles = useCallback(async () => {
    if (loading || !user) return;

    setLoading(true);
    console.log('loadMoreProfiles called. Current lastVisible:', lastVisible);
    try {
      const { profiles: newProfiles, lastVisible: newLastVisible } = await getProfiles(
        user.id,
        undefined, // gender filter
        undefined, // preference filter
        10, // limit
        lastVisible
      );

      if (newProfiles.length > 0) {
        setDiscoveries(prev => {
          const updatedDiscoveries = [...prev, ...newProfiles];
          console.log('Discoveries updated. New length:', updatedDiscoveries.length);
          return updatedDiscoveries;
        });
        setLastVisible(newLastVisible);
        console.log('New lastVisible:', newLastVisible);
      } else {
        console.log('No new profiles fetched.');
        setHasMoreData(false);
      }
    } catch (error) {
      console.error('Failed to load more profiles:', error);
    } finally {
      setLoading(false);
      console.log('loadMoreProfiles finished. Loading set to false.');
    }
  },
 [loading, user, lastVisible]);

  // Helper function to calculate distance between two locations
  const calculateDistance = (location1: LocationType | null, location2: LocationType | null): number => {
    if (!location1 || !location2) return 0;
    
    // Simple distance calculation (you might want to use a more accurate formula)
    const distance = Math.sqrt(
      Math.pow(location2.latitude - location1.latitude, 2) + 
      Math.pow(location2.longitude - location1.longitude, 2)
    ) * 69; // Rough miles conversion
    
    return Math.round(distance);
  };

  // Function to handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    setLastVisible(null);
    loadMoreProfiles();
  },
 [loadMoreProfiles]);
  
  // Create PanResponder for handling vertical swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gesture) => {
        // Only allow vertical movement
        position.setValue({ x: 0, y: gesture.dy });
        console.log('PanResponderMove: gesture.dy', gesture.dy);
      },
      onPanResponderRelease: (_, gesture) => {
        console.log('PanResponderRelease: gesture.dy', gesture.dy);
        // Swipe handling logic
        const swipeThreshold = 120;
        
        // Vertical swipes for next/previous
        if (Math.abs(gesture.dy) > swipeThreshold) {
          if (gesture.dy < 0) {
            // Swipe up - Next profile
            if (currentIndex < discoveries.length - 1) {
              console.log('Swiping up to next profile');
              Animated.spring(position, {
                toValue: { x: 0, y: -height - 100 },
                useNativeDriver: true
              }).start(() => {
                setCurrentIndex(currentIndex + 1);
                position.setValue({ x: 0, y: 0 });
              });
            } else if (hasMoreData) {
              // No more cards but more data available
              console.log('Swiping up, loading more profiles');
              Animated.spring(position, {
                toValue: { x: 0, y: -height - 100 },
                useNativeDriver: true
              }).start(() => {
                loadMoreProfiles();
                position.setValue({ x: 0, y: 0 });
              });
            } else {
              // No more profiles, reset position
              console.log('No more profiles, resetting position');
              resetPosition();
            }
          } else {
            // Swipe down - Previous profile (if available)
            if (currentIndex > 0) {
              console.log('Swiping down to previous profile');
              Animated.spring(position, {
                toValue: { x: 0, y: height + 100 },
                useNativeDriver: true
              }).start(() => {
                setCurrentIndex(currentIndex - 1);
                position.setValue({ x: 0, y: 0 });
              });
            } else {
              // No previous profile, reset position
              console.log('No previous profile, resetting position');
              resetPosition();
            }
          }
        } else {
          // Reset position if swipe was not strong enough
          console.log('Swipe not strong enough, resetting position');
          resetPosition();
        }
      }
    })
  ).current;
  
  // Helper function to reset card position
  const resetPosition = useCallback(() => {
    console.log('resetPosition called');
    Animated.spring(position, {
      toValue: { x: 0, y: 0 },
      friction: 4,
      useNativeDriver: true
    }).start();
  },
 [position]);

  // Function to handle sending friend requests
  const handleSendFriendRequest = useCallback((id: string) => {
    try {
      // Check if we've already sent a request to this user
      const alreadyRequested = sentRequests.includes(id);
      
      if (alreadyRequested) {
        // Remove from sent requests (cancel request)
        setSentRequests(sentRequests.filter(reqId => reqId !== id));
        
        // Show feedback
        Alert.alert('Request Canceled', 'Friend request has been canceled.');
      } else {
        // Find the discovery item to get the name
        const discovery = discoveries.find((d: Profile) => d.id === id);
        const recipientName = discovery?.name || 'this user';
        
        // Show message modal
        setSelectedUserId(id);
        setSelectedUserName(recipientName);
        setShowMessageModal(true);
      }
    } catch (error) {
      console.error('Error sending friend request:', error);
      Alert.alert('Error', 'Failed to send friend request. Please try again.');
    }
  },
 [discoveries, sentRequests]);

  // Handle sending the friend request with a message
  const handleSendRequestWithMessage = useCallback(async (message: string) => {
    if (!selectedUserId) return;
    
    try {
      // Add to sent requests
      setSentRequests([...sentRequests, selectedUserId]);
      
      // Get the current user's info
      const currentUserId = user?.id || 'currentUser';
      const currentUserName = user?.displayName || userName;
      const currentUserPhoto = user?.photoURL;
      
      // Send a friend request notification with the message
      const success = await sendFriendRequest(
        currentUserId,
        currentUserName,
        currentUserPhoto || '', // Add fallback empty string for undefined
        selectedUserId,
        message.trim() || `Hi ${selectedUserName}, I'd like to connect with you!`
      );

      if (success) {
        // Show personalized feedback
        Alert.alert(
          'Request Sent',
          `Friend request has been sent to ${selectedUserName} successfully!`
        );
      } else {
        // Show error feedback
        Alert.alert(
          'Request Failed',
          'Failed to send friend request. You may have already sent a request to this user.'
        );
        // Remove from sent requests if there was an error
        setSentRequests(sentRequests.filter(id => id !== selectedUserId));
      }
    } catch (error) {
      console.error('Error sending friend request:', error);
      Alert.alert('Error', 'Failed to send friend request. Please try again.');
      // Remove from sent requests if there was an error
      setSentRequests(sentRequests.filter(id => id !== selectedUserId));
    } finally {
      // Reset modal state
      setShowMessageModal(false);
      setSelectedUserId('');
      setSelectedUserName('');
    }
  },
 [user, userName, sentRequests, selectedUserId, selectedUserName]);

  // Function to handle saving profiles
  const handleSaveProfile = useCallback((id: string) => {
    // Check if profile is already saved
    const alreadySaved = savedProfiles.includes(id);
    
    if (alreadySaved) {
      // Remove from saved profiles
      setSavedProfiles(savedProfiles.filter(id => id !== id));
    } else {
      // Add to saved profiles
      setSavedProfiles([...savedProfiles, id]);
    }
    
    // Log the action
    console.log(alreadySaved ? 
      `Removed user ${id} from saved profiles` : 
      `Saved user ${id} to profiles`);
  },
 [savedProfiles]);

  // Function to handle viewing profile details
  const handleViewDetails = useCallback((profile: Profile) => {
    // Navigate to profile details page
    router.push({
      pathname: '/profile-details/[id]',
      params: { id: profile.id }
    });
  },
 [router]);



  useEffect(() => {
    if (user) {
      console.log('User available, loading initial profiles...');
      loadMoreProfiles();
      fetchFriendRequestCount();
    }
  },
 [user, loadMoreProfiles, fetchFriendRequestCount]);

  useEffect(() => {
    console.log('Current discoveries length:', discoveries.length);
  },
 [discoveries.length]);

  // Add the useEffect for getting location inside the component
  useEffect(() => {
    // Get user location
    const getUserLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        
        if (status !== 'granted') {
          console.log('Permission to access location was denied');
          return;
        }
        
        const location = await Location.getCurrentPositionAsync({});
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        });
      } catch (error) {
        console.error('Error getting location:', error);
      }
    };
    
    getUserLocation();
  },
 []);

  if (loading && discoveries.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: getThemeColor('background') }]}>
        <View style={StyleSheet.absoluteFill}>
          <LinearGradient
            colors={
              colorScheme === 'dark' 
                ? ['#121212', '#1E1E1E', '#282828'] 
                : ['#F5F7FA', '#E4E8F0', '#D3D8E3']
            }
            style={{ flex: 1 }}
          />
        </View>
        <View style={styles.loadingContainer}>
          <IconSymbol name="sparkles" size={50} color={Colors.secondary} />
          <Text style={[
            styles.loadingText,
            { color: Colors.dark.text }
          ]}>
            Finding people near you...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (discoveries.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: getThemeColor('background') }]}>
        <View style={StyleSheet.absoluteFill}>
          <LinearGradient
            colors={
              colorScheme === 'dark' 
                ? ['#121212', '#1E1E1E', '#282828'] 
                : ['#F5F7FA', '#E4E8F0', '#D3D8E3']
            }
            style={{ flex: 1 }}
          />
        </View>
        <View style={styles.emptyContainer}>
          <IconSymbol 
            name="magnifyingglass"
            size={50}
            color={Colors.primary}
          />
          <Text style={styles.emptyTitle}>
            No one new around you
          </Text>
          <Text style={styles.emptyText}>
            Try changing your preferences or check back later
          </Text>
          <Button 
            title="Refresh" 
            onPress={handleRefresh} 
            style={styles.refreshButton}
            icon={<IconSymbol name="arrow.clockwise" size={16} color="#fff" />}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} />

      {/* Header Section */}
      <View style={styles.headerContainer}>
        <View style={[styles.header, { backgroundColor: Colors.dark.background, borderBottomColor: Colors.dark.text }]}>
          <ThemedText style={{ fontSize: 24, fontWeight: 'bold', color: Colors.primary }}>Discover</ThemedText>
          <TouchableOpacity onPress={() => router.push('/(tabs)/profile')}>
            {/* <Image source={{ uri: user?.avatar_url || 'https://via.placeholder.com/50' }} style={styles.profileImage} /> */}
            {/* <Image source={require('../../assets/images/logo.png')} style={styles.logo} /> */}
          </TouchableOpacity>
          <TouchableOpacity onPress={() => router.push('/friend-requests')} style={styles.friendRequestButton}>
            <IconSymbol name="person.badge.plus" size={30} color={Colors.primary} />
            {friendRequestCount > 0 && (
              <View style={styles.friendRequestBadge}>
                <Text style={styles.friendRequestBadgeText}>
                  {friendRequestCount > 99 ? '99+' : friendRequestCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Gap below header */}
      <View style={{ height: GAP_HEIGHT }} />

      {/* Card Section */}
      <View style={styles.cardsContainer}>
        {discoveries.length > 0 ? (
          <Animated.View
            key={discoveries[currentIndex]?.id}
            style={[
              styles.animatedCardContainer,
              { transform: position.getTranslateTransform() }
            ]}
            {...panResponder.panHandlers}
            onLayout={(event) => {
              const { x, y, width, height } = event.nativeEvent.layout;
              console.log('Animated.View layout:', { x, y, width, height });
              console.log('Animated.View transform:', position.getTranslateTransform());
            }}
          >
            <DiscoveryCard
              item={discoveries[currentIndex]}
              onSendFriendRequest={handleSendFriendRequest}
              onSaveProfile={handleSaveProfile}
              onViewDetails={handleViewDetails}
              isFriendRequested={sentRequests.includes(discoveries[currentIndex]?.id || '')}
              isSaved={savedProfiles.includes(discoveries[currentIndex]?.id || '')}
              colorScheme={colorScheme ?? null}
            />
          </Animated.View>
        ) : (
          <View style={styles.noMoreProfilesContainer}>
            <ThemedText style={styles.noMoreProfilesText}>No more profiles for now. Check back later!</ThemedText>
            <Button title="Refresh" onPress={handleRefresh} />
          </View>
        )}
      </View>

      {/* Gap above navigation */}
      <View style={{ height: GAP_HEIGHT }} />




      <MessageInputModal
        visible={showMessageModal}
        title="Add a Message"
        message={`Add a personal message to send with your friend request to ${selectedUserName}:`}
        defaultValue={`Hi ${selectedUserName}, I'd like to connect with you!`}
        onCancel={() => {
          setShowMessageModal(false);
          setSelectedUserId(null);
          setSelectedUserName('');
        }}
        onSubmit={handleSendRequestWithMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background, // Ensure background color is set
  },
  headerContainer: {
    height: HEADER_HEIGHT,
    paddingHorizontal: 16,
    paddingTop: 0,
    zIndex: 10,
    justifyContent: 'center', // Center content vertically
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    backgroundColor: Colors.dark.background,
    borderBottomColor: Colors.dark.text,
    width: '100%',
    zIndex: 1,
    elevation: 1,
  },
  cardsContainer: {
    height: CARD_SECTION_HEIGHT,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SIDE_GAP,
  },
  animatedCardContainer: {
    position: 'absolute',
    width: '95%', // Make the card responsive
    height: CARD_HEIGHT, // Use the responsive CARD_HEIGHT
    alignSelf: 'center',
  },
  card: {
    width: '95%', // Make the card responsive
    height: '100%', // Card takes full height of its container
    position: 'absolute',
    alignSelf: 'center',
    flexDirection: 'column',
    borderWidth: 1,
    borderColor: Colors.dark.text,
    padding: 20,
    marginVertical: 10,
    borderRadius: 16, // Added for rounded corners
    overflow: 'hidden', // Ensure content respects border radius
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
  },
  cardUpperSection: {
    flex: 0.7,
    overflow: 'hidden',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  imageContainer: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '95%', // Make the card responsive
    height: '100%',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageIndicator: {
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 2,
  },
  cardLowerSection: {
    flex: 0.3,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  lowerSectionGradient: {
    flex: 1,
    padding: 16,
  },
  cardContentContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  cardMainContent: {
    flex: 1,
  },
  nameAgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 5,
  },
  nameText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 10,
    color: Colors.dark.text,
  },
  ageText: {
    fontSize: 22,
    fontWeight: '500',
    color: Colors.dark.text,
  },
  distanceText: {
    fontSize: 14,
    marginBottom: 8,
    color: Colors.dark.text,
  },
  bioText: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 8,
    color: Colors.dark.text,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
    width: '80%',
  },
    interestTag: {
    backgroundColor: Colors.lightGray,
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginRight: 5,
    marginBottom: 5,
    gap: 5,
  },
  interestText: {
      fontSize: 14,
      color: Colors.darkGray,
    },
    locationHobbiesContainer: {
    flexDirection: 'column',
    marginTop: 5,
    paddingHorizontal: 5,
    marginBottom: 16, // Adjusted for more space at the bottom of the card
    width: '80%',
    gap: 5,
  },
    locationText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: Colors.dark.text,
    },
    hobbiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
    marginBottom: 5,
    gap: 5,
  },
  hobbyTag: {
    backgroundColor: Colors.primary,
    borderRadius: 15,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 4,
    marginBottom: 4,
    gap: 5,
  },
  hobbyText: {
      fontSize: 14,
      color: Colors.light.text,
    },
    sideButtonsContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'space-around',
      height: '100%',
    },
    sideActionButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 2,
    },
  
  // Keep these styles for backward compatibility but they won't be used
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonsContainerIOS: {
    justifyContent: 'flex-end',
  },
  iosButtonsGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 16,
  },
  iosActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spacer: {
    flex: 1,
  },
  cardActionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 75, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  primaryButton: {
    width: 140,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
  },
  secondaryButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonGradient: {
    width: '100%',
    height: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    opacity: 0.7,
  },
  refreshButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  bottomNavigation: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      backgroundColor: Colors.light.background,
      paddingVertical: 10,
      borderTopWidth: 1,
      borderTopColor: Colors.light.tint,
    },
    navButton: {
      alignItems: 'center',
      padding: 10,
    },
    noMoreProfilesContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    noMoreProfilesText: {
      fontSize: 18,
      textAlign: 'center',
      marginBottom: 20,
    },
    noImagePlaceholder: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#1E1E1E',
    },
    noImageText: {
      color: '#666666',
      fontSize: 14,
      marginTop: 8,
    },
    friendRequestButton: {
      position: 'relative',
    },
    friendRequestBadge: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: '#FF4B00',
      borderRadius: 12,
      paddingHorizontal: 6,
      paddingVertical: 2,
      minWidth: 24,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: '#FFFFFF',
    },
    friendRequestBadgeText: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: 'bold',
    },
  });


