import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { decode } from 'base64-arraybuffer';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import { useEffect, useState } from 'react';
import { Alert, FlatList, Image, Platform, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { PageContainer } from '../../components/layout/PageContainer';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';

interface DiscoverCardImage {
  id: string;
  user_id: string;
  image_url: string;
  created_at: string;
}

export default function DiscoverCardScreen() {
  const navigation = useNavigation();
  const [images, setImages] = useState<DiscoverCardImage[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchImages();
    }
  }, [user]);

  const fetchImages = async () => {
    if (!user) return;
    setLoading(true);
    const { data, error } = await supabase
      .from('discover_card_images')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching images:', error.message);
      Alert.alert('Error', 'Failed to fetch images.');
    } else {
      setImages(data || []);
    }
    setLoading(false);
  };

  const pickImage = async () => {
    if (!user) {
      Alert.alert('Error', 'User not authenticated.');
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const asset = result.assets[0];
      const fileExtension = asset.uri.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExtension}`;
      const filePath = `discover_cards/${fileName}`;

      // Read the image as base64
      const base64 = await FileSystem.readAsStringAsync(asset.uri, { encoding: FileSystem.EncodingType.Base64 });

      if (!base64) {
        Alert.alert('Error', 'Could not read image data.');
        return;
      }

      const { data, error } = await supabase.storage
        .from('images')
        .upload(filePath, decode(base64), {
          contentType: `image/${fileExtension}`,
          upsert: false,
        });

      if (error) {
        console.error('Error uploading image:', error.message);
        Alert.alert('Error', 'Failed to upload image.');
      } else {
        const publicUrl = supabase.storage.from('images').getPublicUrl(filePath).data.publicUrl;
        await saveImageUrlToDatabase(publicUrl);
      }
    }
  };

  const saveImageUrlToDatabase = async (imageUrl: string) => {
    if (!user) return;
    const { data, error } = await supabase
      .from('discover_card_images')
      .insert({
        user_id: user.id,
        image_url: imageUrl,
      });

    if (error) {
      console.error('Error saving image URL to database:', error.message);
      Alert.alert('Error', 'Failed to save image URL.');
    } else {
      Alert.alert('Success', 'Image uploaded and saved!');
      fetchImages(); // Refresh the list of images
    }
  };

  const renderItem = ({ item }: { item: DiscoverCardImage }) => (
    <View style={styles.imageItem}>
      <Image source={{ uri: item.image_url }} style={styles.image} />
    </View>
  );

  return (
    <PageContainer>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Your Discover Card Images</Text>
          <View style={styles.rightSpacer} />
        </View>
      </SafeAreaView>
      <View style={styles.container}>
        {loading ? (
          <Text>Loading images...</Text>
        ) : images.length === 0 ? (
          <Text>No discover card images uploaded yet.</Text>
        ) : (
          <FlatList
            data={images}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            contentContainerStyle={styles.imageList}
          />
        )}
        <TouchableOpacity style={styles.addButton} onPress={pickImage}>
          <Text style={styles.addButtonText}>+</Text>
        </TouchableOpacity>
      </View>
    </PageContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  safeArea: {
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === 'ios' ? 0 : 10, // Adjust padding for iOS
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    width: '100%',
    height: Platform.OS === 'ios' ? 44 : 'auto', // Standard iOS header height
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  rightSpacer: {
    width: 34, // Same width as the back button to balance the title
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 30,
    marginTop: 20,
  },
  imageList: {
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  imageItem: {
    margin: 8,
    borderRadius: 15,
    overflow: 'hidden',
    backgroundColor: '#fff',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: 160,
    height: 160,
    resizeMode: 'cover',
    borderRadius: 15,
  },
  addButton: {
    backgroundColor: '#6A0DAD',
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 40,
    right: 30,
    elevation: 8,
    shadowColor: '#6A0DAD',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  addButtonText: {
    color: 'white',
    fontSize: 36,
    fontWeight: 'bold',
  },
});