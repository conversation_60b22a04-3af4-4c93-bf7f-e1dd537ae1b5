import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
    <PERSON>ert,
    FlatList,
    Image,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { IconSymbol } from '../components/ui/IconSymbol';
import { Colors } from '../constants/Colors';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

interface FriendRequest {
  id: string;
  sender_id: string;
  receiver_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  message?: string;
  created_at: string;
  sender_profile: {
    id: string;
    display_name: string;
    avatar_url?: string;
    bio?: string;
    age?: number;
    location?: string;
  };
}

export default function FriendRequestsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch friend requests
  const fetchFriendRequests = async () => {
    if (!user?.id) return;

    try {
      console.log('Fetching friend requests for user:', user.id);

      // Test query to see all friend requests in the database
      const { data: allRequests, error: allError } = await supabase
        .from('friend_requests')
        .select('*');
      console.log('All friend requests in database:', allRequests);

      // First get the friend requests
      const { data: requestsData, error: requestsError } = await supabase
        .from('friend_requests')
        .select('*')
        .eq('receiver_id', user.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (requestsError) {
        console.error('Error fetching friend requests:', requestsError);
        return;
      }

      console.log('Found friend requests:', requestsData);

      if (!requestsData || requestsData.length === 0) {
        setFriendRequests([]);
        setLoading(false);
        setRefreshing(false);
        return;
      }

      // Get sender profiles for each request
      const senderIds = requestsData.map(req => req.sender_id);
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url, bio, birthdate, location')
        .in('id', senderIds);

      if (profilesError) {
        console.error('Error fetching sender profiles:', profilesError);
        return;
      }

      console.log('Found sender profiles:', profilesData);

      // Combine requests with sender profiles
      const processedData = requestsData.map(request => {
        const senderProfile = profilesData?.find(profile => profile.id === request.sender_id);
        return {
          ...request,
          sender_profile: {
            ...senderProfile,
            age: senderProfile?.birthdate
              ? new Date().getFullYear() - new Date(senderProfile.birthdate).getFullYear()
              : undefined
          }
        };
      });

      console.log('Processed friend requests:', processedData);
      setFriendRequests(processedData);
    } catch (error) {
      console.error('Error in fetchFriendRequests:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle accepting friend request
  const handleAcceptRequest = async (requestId: string, senderId: string) => {
    try {
      // Update friend request status
      const { error: updateError } = await supabase
        .from('friend_requests')
        .update({ status: 'accepted' })
        .eq('id', requestId);

      if (updateError) {
        console.error('Error accepting friend request:', updateError);
        Alert.alert('Error', 'Failed to accept friend request');
        return;
      }

      // No need to create separate friendship records
      // The accepted friend_request serves as the friendship record

      // Remove from local state
      setFriendRequests(prev => prev.filter(req => req.id !== requestId));
      Alert.alert('Success', 'Friend request accepted!');
    } catch (error) {
      console.error('Error in handleAcceptRequest:', error);
      Alert.alert('Error', 'Failed to accept friend request');
    }
  };

  // Handle rejecting friend request
  const handleRejectRequest = async (requestId: string) => {
    try {
      const { error } = await supabase
        .from('friend_requests')
        .update({ status: 'rejected' })
        .eq('id', requestId);

      if (error) {
        console.error('Error rejecting friend request:', error);
        Alert.alert('Error', 'Failed to reject friend request');
        return;
      }

      // Remove from local state
      setFriendRequests(prev => prev.filter(req => req.id !== requestId));
      Alert.alert('Success', 'Friend request rejected');
    } catch (error) {
      console.error('Error in handleRejectRequest:', error);
      Alert.alert('Error', 'Failed to reject friend request');
    }
  };

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    fetchFriendRequests();
  };

  useEffect(() => {
    console.log('Friend requests useEffect triggered, user:', user);
    console.log('User ID:', user?.id);
    fetchFriendRequests();
  }, [user?.id]);

  // Render friend request item
  const renderFriendRequest = ({ item }: { item: FriendRequest }) => (
    <View style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <Image
          source={{ 
            uri: item.sender_profile.avatar_url || 'https://via.placeholder.com/60' 
          }}
          style={styles.avatar}
        />
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.sender_profile.display_name}</Text>
          {item.sender_profile.age && (
            <Text style={styles.userAge}>{item.sender_profile.age} years old</Text>
          )}
          {item.sender_profile.location && (
            <Text style={styles.userLocation}>{item.sender_profile.location}</Text>
          )}
        </View>
      </View>

      {item.message && (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>"{item.message}"</Text>
        </View>
      )}

      {item.sender_profile.bio && (
        <Text style={styles.bioText} numberOfLines={2}>
          {item.sender_profile.bio}
        </Text>
      )}

      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>
          {new Date(item.created_at).toLocaleDateString()} at{' '}
          {new Date(item.created_at).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.rejectButton]}
          onPress={() => handleRejectRequest(item.id)}
        >
          <IconSymbol name="xmark" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Reject</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.acceptButton]}
          onPress={() => handleAcceptRequest(item.id, item.sender_id)}
        >
          <IconSymbol name="checkmark" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Accept</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Friend Requests</Text>
        <View style={styles.headerRight}>
          {friendRequests.length > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{friendRequests.length}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Content */}
      {loading ? (
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>Loading friend requests...</Text>
        </View>
      ) : friendRequests.length === 0 ? (
        <View style={styles.centerContainer}>
          <IconSymbol name="person.badge.plus" size={60} color={Colors.lightGray} />
          <Text style={styles.emptyTitle}>No Friend Requests</Text>
          <Text style={styles.emptyText}>
            You don't have any pending friend requests at the moment.
          </Text>
        </View>
      ) : (
        <FlatList
          data={friendRequests}
          renderItem={renderFriendRequest}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  listContainer: {
    padding: 16,
  },
  requestCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  userAge: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  userLocation: {
    fontSize: 14,
    color: '#666',
  },
  messageContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    fontStyle: 'italic',
  },
  bioText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  timeContainer: {
    marginBottom: 16,
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  rejectButton: {
    backgroundColor: '#FF6B6B',
  },
  acceptButton: {
    backgroundColor: Colors.primary,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
