import { Blur<PERSON>iew } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Image,
  Linking,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { Button } from '../../components/ui/Button';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';

const { width, height } = Dimensions.get('window');
const isIOS = Platform.OS === 'ios';

// Helper function to get full image URL from Supabase storage
const getImageUrl = (imagePath: string, bucket: 'images' | 'avatars' = 'images') => {
  if (!imagePath) return null;

  // If it's already a full URL (http/https), return as is
  if (imagePath.startsWith('http')) return imagePath;

  // If it's a local file path, return as is (for local images)
  if (imagePath.startsWith('file://')) return imagePath;

  // Construct Supabase storage URL for relative paths
  const supabaseUrl = 'https://upijhrlbwqliwyxqamfx.supabase.co';
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${imagePath}`;
};

// Define interfaces for better type safety
interface ProfileData {
  id: string;
  display_name?: string;
  bio?: string;
  gender?: string;
  location?: string;
  occupation?: string;
  education?: string;
  avatar_url?: string;
  photos?: string[];
  hobbies?: string[];
  birthdate?: string;
  email?: string;
  phone_number?: string;
  coin_balance?: number;
  preference?: any;
  auto_pay_preference?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SocialMediaLinks {
  id: string;
  user_id: string;
  instagram?: string;
  facebook?: string;
  created_at?: string;
}

export default function ProfileDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();

  // State management
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [socialLinks, setSocialLinks] = useState<SocialMediaLinks | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [reportReason, setReportReason] = useState('');
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [bioExpanded, setBioExpanded] = useState(false);

  useEffect(() => {
    if (id) {
      fetchProfile();
      checkUserInteractions();
    }
  }, [id]);

  const fetchProfile = async () => {
    try {
      setLoading(true);

      // Fetch profile data
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        Alert.alert('Error', 'Could not load profile. Please try again.');
        return;
      }

      // Fetch social media links
      const { data: socialData, error: socialError } = await supabase
        .from('social_media_links')
        .select('*')
        .eq('user_id', id)
        .single();

      if (socialError && socialError.code !== 'PGRST116') {
        console.error('Error fetching social links:', socialError);
      }

      setProfile(profileData);
      setSocialLinks(socialData);

      // Debug logging
      console.log('Profile data:', profileData);
      console.log('Social data:', socialData);
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkUserInteractions = async () => {
    if (!user?.id || !id) return;

    try {
      // Check if user has liked this profile
      const { data: likeData } = await supabase
        .from('likes')
        .select('id')
        .eq('user_id', user.id)
        .eq('liked_user_id', id)
        .single();

      setIsLiked(!!likeData);

      // Check if user has saved this profile
      const { data: saveData } = await supabase
        .from('saved_profiles')
        .select('id')
        .eq('user_id', user.id)
        .eq('saved_user_id', id)
        .single();

      setIsSaved(!!saveData);

      // Check if user has blocked this profile
      const { data: blockData } = await supabase
        .from('blocked_users')
        .select('id')
        .eq('user_id', user.id)
        .eq('blocked_user_id', id)
        .single();

      setIsBlocked(!!blockData);
    } catch (error) {
      console.error('Error checking user interactions:', error);
    }
  };

  // Action handlers
  const handleLike = useCallback(async () => {
    if (!user?.id || !id) return;

    try {
      if (isLiked) {
        // Unlike
        await supabase
          .from('likes')
          .delete()
          .eq('user_id', user.id)
          .eq('liked_user_id', id);
        setIsLiked(false);
      } else {
        // Like
        await supabase
          .from('likes')
          .insert({
            user_id: user.id,
            liked_user_id: id,
            created_at: new Date().toISOString()
          });
        setIsLiked(true);
      }
    } catch (error) {
      console.error('Error handling like:', error);
      Alert.alert('Error', 'Failed to update like status');
    }
  }, [isLiked, user?.id, id]);

  const handleSave = useCallback(async () => {
    if (!user?.id || !id) return;

    try {
      if (isSaved) {
        // Unsave
        await supabase
          .from('saved_profiles')
          .delete()
          .eq('user_id', user.id)
          .eq('saved_user_id', id);
        setIsSaved(false);
      } else {
        // Save
        await supabase
          .from('saved_profiles')
          .insert({
            user_id: user.id,
            saved_user_id: id,
            created_at: new Date().toISOString()
          });
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Error handling save:', error);
      Alert.alert('Error', 'Failed to update save status');
    }
  }, [isSaved, user?.id, id]);

  const handleSendMessage = useCallback(async () => {
    if (!messageText.trim() || !user?.id || !id) return;

    try {
      // Create or get conversation
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .select('id')
        .or(`and(user1_id.eq.${user.id},user2_id.eq.${id}),and(user1_id.eq.${id},user2_id.eq.${user.id})`)
        .single();

      let conversationId = conversation?.id;

      if (!conversationId) {
        // Create new conversation
        const { data: newConv, error: newConvError } = await supabase
          .from('conversations')
          .insert({
            user1_id: user.id,
            user2_id: id,
            created_at: new Date().toISOString()
          })
          .select('id')
          .single();

        if (newConvError) throw newConvError;
        conversationId = newConv.id;
      }

      // Send message
      const { error: messageError } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          content: messageText.trim(),
          created_at: new Date().toISOString()
        });

      if (messageError) throw messageError;

      setMessageText('');
      setShowMessageModal(false);
      Alert.alert('Success', 'Message sent successfully!');
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  }, [messageText, user?.id, id]);

  const handleReport = useCallback(async () => {
    if (!reportReason.trim() || !user?.id || !id) return;

    try {
      await supabase
        .from('reports')
        .insert({
          reporter_id: user.id,
          reported_user_id: id,
          reason: reportReason.trim(),
          created_at: new Date().toISOString()
        });

      setReportReason('');
      setShowReportModal(false);
      Alert.alert('Success', 'Report submitted successfully');
    } catch (error) {
      console.error('Error submitting report:', error);
      Alert.alert('Error', 'Failed to submit report');
    }
  }, [reportReason, user?.id, id]);

  const handleSocialMediaPress = useCallback((platform: string, url: string) => {
    if (url) {
      // Ensure URL has proper protocol
      const fullUrl = url.startsWith('http') ? url : `https://${url}`;
      Linking.openURL(fullUrl).catch(() => {
        Alert.alert('Error', 'Could not open link');
      });
    }
  }, []);

  const handleBlock = useCallback(async () => {
    if (!user?.id || !id) return;

    Alert.alert(
      'Block User',
      'Are you sure you want to block this user? You won\'t see their profile anymore.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Block',
          style: 'destructive',
          onPress: async () => {
            try {
              await supabase
                .from('blocked_users')
                .insert({
                  user_id: user.id,
                  blocked_user_id: id,
                  created_at: new Date().toISOString()
                });

              setIsBlocked(true);
              Alert.alert('Success', 'User blocked successfully');
              router.back();
            } catch (error) {
              console.error('Error blocking user:', error);
              Alert.alert('Error', 'Failed to block user');
            }
          }
        }
      ]
    );
  }, [user?.id, id, router]);



  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF4B00" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={48} color="#FF4B00" />
          <Text style={styles.errorText}>Profile not found</Text>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            variant="primary"
            style={styles.errorButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Process images with correct URLs
  const processedPhotos = profile.photos?.map(photo => getImageUrl(photo, 'images')).filter((url): url is string => Boolean(url)) || [];
  const avatarUrl = profile.avatar_url ? getImageUrl(profile.avatar_url, 'avatars') : null;

  const profileImages: string[] = processedPhotos.length > 0
    ? processedPhotos
    : (avatarUrl ? [avatarUrl] : []);


  const displayName = profile.display_name || 'Unknown User';

  // Calculate age from birthdate
  const calculateAge = (birthdate: string) => {
    const today = new Date();
    const birth = new Date(birthdate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const age = profile.birthdate ? calculateAge(profile.birthdate) : null;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
          <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity onPress={() => setShowReportModal(true)} style={styles.headerButton}>
          <IconSymbol name="ellipsis-horizontal" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        <View style={styles.imageGalleryContainer}>
          {profileImages.length > 0 ? (
            <>
              <FlatList
                data={profileImages}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onMomentumScrollEnd={(event) => {
                  const index = Math.round(event.nativeEvent.contentOffset.x / width);
                  setCurrentImageIndex(index);
                }}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    onPress={() => setShowImageModal(true)}
                    style={styles.imageContainer}
                  >
                    <Image
                      source={{ uri: item }}
                      style={styles.profileImage}
                    />
                    <LinearGradient
                      colors={['transparent', 'rgba(0,0,0,0.7)']}
                      style={styles.imageGradient}
                    />
                  </TouchableOpacity>
                )}
                keyExtractor={(item, index) => index.toString()}
              />

              {/* Image indicators */}
              {profileImages.length > 1 && (
                <View style={styles.imageIndicators}>
                  {profileImages.map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.indicator,
                        index === currentImageIndex && styles.activeIndicator
                      ]}
                    />
                  ))}
                </View>
              )}
            </>
          ) : (
            <View style={styles.noImageContainer}>
              <IconSymbol name="person" size={80} color="#666666" />
              <Text style={styles.noImageText}>No photos available</Text>
            </View>
          )}
        </View>

        {/* Profile Info */}
        <View style={styles.profileInfo}>
          {/* Name and Age */}
          <View style={styles.nameSection}>
            <Text style={styles.profileName}>
              {displayName}
              {age && <Text style={styles.age}>, {age}</Text>}
            </Text>
            {profile.location && (
              <View style={styles.locationContainer}>
                <IconSymbol name="location" size={16} color="#CCCCCC" />
                <Text style={styles.location}>{profile.location}</Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              onPress={handleLike}
              style={[styles.actionButton, isLiked && styles.likedButton]}
            >
              <LinearGradient
                colors={isLiked ? ['#FF4B00', '#FF6B00'] : ['#333333', '#444444']}
                style={styles.actionButtonGradient}
              >
                <IconSymbol
                  name={isLiked ? "heart.fill" : "heart"}
                  size={24}
                  color="#FFFFFF"
                />
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setShowMessageModal(true)}
              style={styles.actionButton}
            >
              <LinearGradient
                colors={['#FF4B00', '#FF6B00']}
                style={styles.actionButtonGradient}
              >
                <IconSymbol name="message" size={24} color="#FFFFFF" />
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSave}
              style={[styles.actionButton, isSaved && styles.savedButton]}
            >
              <LinearGradient
                colors={isSaved ? ['#FF4B00', '#FF6B00'] : ['#333333', '#444444']}
                style={styles.actionButtonGradient}
              >
                <IconSymbol
                  name={isSaved ? "bookmark.fill" : "bookmark"}
                  size={24}
                  color="#FFFFFF"
                />
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Bio Section */}
          {profile.bio && (
            <View style={styles.bioSection}>
              <Text style={styles.sectionTitle}>About</Text>
              <Text
                style={styles.bioText}
                numberOfLines={bioExpanded ? undefined : 3}
              >
                {profile.bio}
              </Text>
              {profile.bio.length > 150 && (
                <TouchableOpacity onPress={() => setBioExpanded(!bioExpanded)}>
                  <Text style={styles.expandText}>
                    {bioExpanded ? 'Show less' : 'Show more'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Basic Info */}
          <View style={styles.infoSection}>
            <Text style={styles.sectionTitle}>Basic Info</Text>
            <View style={styles.infoGrid}>
              {profile.gender && (
                <View style={styles.infoItem}>
                  <IconSymbol name="person" size={20} color="#FF4B00" />
                  <Text style={styles.infoLabel}>Gender</Text>
                  <Text style={styles.infoValue}>{profile.gender}</Text>
                </View>
              )}

              {profile.occupation && (
                <View style={styles.infoItem}>
                  <IconSymbol name="briefcase" size={20} color="#FF4B00" />
                  <Text style={styles.infoLabel}>Occupation</Text>
                  <Text style={styles.infoValue}>{profile.occupation}</Text>
                </View>
              )}
              {profile.education && (
                <View style={styles.infoItem}>
                  <IconSymbol name="graduationcap" size={20} color="#FF4B00" />
                  <Text style={styles.infoLabel}>Education</Text>
                  <Text style={styles.infoValue}>{profile.education}</Text>
                </View>
              )}
            </View>
          </View>

          {/* Hobbies */}
          {(profile.hobbies?.length ?? 0) > 0 && (
            <View style={styles.interestsSection}>
              <Text style={styles.sectionTitle}>Hobbies</Text>
              <View style={styles.tagsContainer}>
                {profile.hobbies?.map((item: string, index: number) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{item}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Social Media */}
          <View style={styles.socialSection}>
            <Text style={styles.sectionTitle}>Connect on Social Media</Text>
            <Text style={styles.socialDescription}>Follow and connect on social platforms</Text>

            {/* Debug info */}
            <Text style={styles.socialDescription}>
              Debug: socialLinks = {socialLinks ? JSON.stringify(socialLinks) : 'null'}
            </Text>

            <View style={styles.socialLinks}>
              {/* Always show Instagram for testing */}
              <TouchableOpacity
                style={[styles.socialButton, styles.instagramButton]}
                onPress={() => handleSocialMediaPress('instagram', socialLinks?.instagram || 'https://instagram.com')}
              >
                <LinearGradient
                  colors={['#833AB4', '#FD1D1D', '#FCB045']}
                  style={styles.socialButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <IconSymbol name="camera" size={24} color="#FFFFFF" />
                  <Text style={styles.socialText}>Instagram</Text>
                  <IconSymbol name="arrow.up.right" size={16} color="#FFFFFF" />
                </LinearGradient>
              </TouchableOpacity>

              {/* Always show Facebook for testing */}
              <TouchableOpacity
                style={[styles.socialButton, styles.facebookButton]}
                onPress={() => handleSocialMediaPress('facebook', socialLinks?.facebook || 'https://facebook.com')}
              >
                <LinearGradient
                  colors={['#1877F2', '#42A5F5']}
                  style={styles.socialButtonGradient}
                >
                  <IconSymbol name="person.2.fill" size={24} color="#FFFFFF" />
                  <Text style={styles.socialText}>Facebook</Text>
                  <IconSymbol name="arrow.up.right" size={16} color="#FFFFFF" />
                </LinearGradient>
              </TouchableOpacity>

              {/* Show actual data if available */}
              {socialLinks?.instagram && (
                <Text style={styles.socialDescription}>
                  Real Instagram: {socialLinks.instagram}
                </Text>
              )}
              {socialLinks?.facebook && (
                <Text style={styles.socialDescription}>
                  Real Facebook: {socialLinks.facebook}
                </Text>
              )}
            </View>
          </View>

          {/* Contact Info */}
          {(profile.email || profile.phone_number) && (
            <View style={styles.additionalSection}>
              <Text style={styles.sectionTitle}>Contact Info</Text>
              <View style={styles.infoGrid}>
                {profile.email && (
                  <View style={styles.infoItem}>
                    <IconSymbol name="envelope" size={20} color="#FF4B00" />
                    <Text style={styles.infoLabel}>Email</Text>
                    <Text style={styles.infoValue}>{profile.email}</Text>
                  </View>
                )}
                {profile.phone_number && (
                  <View style={styles.infoItem}>
                    <IconSymbol name="phone" size={20} color="#FF4B00" />
                    <Text style={styles.infoLabel}>Phone</Text>
                    <Text style={styles.infoValue}>{profile.phone_number}</Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Preferences */}
          {profile.preference && (
            <View style={styles.additionalSection}>
              <Text style={styles.sectionTitle}>Preferences</Text>
              <View style={styles.infoGrid}>
                <View style={styles.infoItem}>
                  <IconSymbol name="heart" size={20} color="#FF4B00" />
                  <Text style={styles.infoLabel}>Looking for</Text>
                  <Text style={styles.infoValue}>{profile.preference}</Text>
                </View>
              </View>
            </View>
          )}

          {/* Report/Block Section */}
          <View style={styles.reportSection}>
            <TouchableOpacity
              onPress={handleBlock}
              style={styles.blockButton}
            >
              <IconSymbol name="xmark.circle" size={20} color="#FF4444" />
              <Text style={styles.blockText}>Block User</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Image Modal */}
      <Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalCloseArea}
            onPress={() => setShowImageModal(false)}
          >
            <View style={styles.imageModalContainer}>
              <FlatList
                data={profileImages}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                initialScrollIndex={currentImageIndex}
                getItemLayout={(data, index) => ({
                  length: width,
                  offset: width * index,
                  index,
                })}
                renderItem={({ item }) => (
                  <Image source={{ uri: item }} style={styles.fullScreenImage} />
                )}
                keyExtractor={(item, index) => index.toString()}
              />
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowImageModal(false)}
              >
                <IconSymbol name="xmark" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Message Modal */}
      <Modal
        visible={showMessageModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMessageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <BlurView intensity={50} style={styles.blurView} tint="dark">
            <View style={styles.messageModalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Send Message</Text>
                <TouchableOpacity onPress={() => setShowMessageModal(false)}>
                  <IconSymbol name="xmark" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
              <TextInput
                style={styles.messageInput}
                placeholder="Type your message..."
                placeholderTextColor="#999999"
                value={messageText}
                onChangeText={setMessageText}
                multiline
                numberOfLines={4}
                autoFocus
              />
              <View style={styles.modalButtons}>
                <Button
                  title="Cancel"
                  onPress={() => setShowMessageModal(false)}
                  variant="outline"
                  style={styles.modalButton}
                />
                <Button
                  title="Send"
                  onPress={handleSendMessage}
                  variant="primary"
                  style={styles.modalButton}
                  disabled={!messageText.trim()}
                />
              </View>
            </View>
          </BlurView>
        </View>
      </Modal>

      {/* Report Modal */}
      <Modal
        visible={showReportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowReportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <BlurView intensity={50} style={styles.blurView} tint="dark">
            <View style={styles.reportModalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Report User</Text>
                <TouchableOpacity onPress={() => setShowReportModal(false)}>
                  <IconSymbol name="xmark" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
              <Text style={styles.reportDescription}>
                Please let us know why you're reporting this user:
              </Text>
              <TextInput
                style={styles.reportInput}
                placeholder="Reason for reporting..."
                placeholderTextColor="#999999"
                value={reportReason}
                onChangeText={setReportReason}
                multiline
                numberOfLines={3}
                autoFocus
              />
              <View style={styles.modalButtons}>
                <Button
                  title="Cancel"
                  onPress={() => setShowReportModal(false)}
                  variant="outline"
                  style={styles.modalButton}
                />
                <Button
                  title="Report"
                  onPress={handleReport}
                  variant="primary"
                  style={styles.modalButton}
                  disabled={!reportReason.trim()}
                />
              </View>
            </View>
          </BlurView>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#121212',
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 16,
  },
  errorButton: {
    marginTop: 16,
  },
  imageGalleryContainer: {
    height: 400,
    marginBottom: 16,
  },
  imageContainer: {
    width: width,
    height: 400,
    position: 'relative',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
  },
  noImageContainer: {
    height: 400,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
  },
  noImageText: {
    color: '#666666',
    fontSize: 16,
    marginTop: 12,
  },
  profileInfo: {
    padding: 16,
  },
  nameSection: {
    marginBottom: 20,
  },
  profileName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  age: {
    fontSize: 24,
    fontWeight: '400',
    color: '#CCCCCC',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  location: {
    fontSize: 16,
    color: '#CCCCCC',
    marginLeft: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
  },
  actionButtonGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  likedButton: {
    // Additional styles for liked state if needed
  },
  savedButton: {
    // Additional styles for saved state if needed
  },
  bioSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  bioText: {
    fontSize: 16,
    color: '#CCCCCC',
    lineHeight: 24,
  },
  expandText: {
    fontSize: 14,
    color: '#FF4B00',
    marginTop: 8,
    fontWeight: '600',
  },
  infoSection: {
    marginBottom: 24,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
    padding: 16,
    borderRadius: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#CCCCCC',
    marginLeft: 12,
    flex: 1,
  },
  infoValue: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  interestsSection: {
    marginBottom: 24,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: '#FF4B00',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },

  additionalSection: {
    marginBottom: 24,
  },
  socialSection: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#1A1A1A',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#333333',
  },
  socialDescription: {
    color: '#CCCCCC',
    fontSize: 14,
    marginBottom: 16,
    marginTop: 4,
  },
  socialLinks: {
    gap: 12,
  },
  socialButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  socialButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  socialText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginLeft: 12,
  },
  instagramButton: {
    marginBottom: 8,
  },
  facebookButton: {
    marginBottom: 8,
  },
  reportSection: {
    marginTop: 32,
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#333333',
  },
  blockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2A1A1A',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF4444',
  },
  blockText: {
    color: '#FF4444',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageModalContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: width,
    height: height * 0.8,
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  blurView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  messageModalContainer: {
    backgroundColor: '#1E1E1E',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  reportModalContainer: {
    backgroundColor: '#1E1E1E',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  reportDescription: {
    fontSize: 16,
    color: '#CCCCCC',
    marginBottom: 16,
    lineHeight: 22,
  },
  messageInput: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 16,
    color: '#FFFFFF',
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  reportInput: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 16,
    color: '#FFFFFF',
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
});



